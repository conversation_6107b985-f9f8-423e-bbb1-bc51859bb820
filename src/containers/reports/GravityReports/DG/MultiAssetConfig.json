{"table": [{"title": "Detailed Data", "apiType": "data", "colSpan": 24, "apiConfig": {"things": [], "summary": [{"group_by": "time", "parameters": ["fuel_consumption_p_calculated_runhour.overall", "calculated_energy_p_fuel_consumption.overall"]}, {"group_by": "thing_id", "parameters": ["fuel_consumption_p_calculated_runhour.overall", "calculated_energy_p_fuel_consumption.overall"]}, {"parameters": ["fuel_consumption_p_calculated_runhour.overall", "calculated_energy_p_fuel_consumption.overall"]}], "api_query": {"thing_category": "18"}, "data_type": "aggregate", "summarize": [{"query": ["sum(calculated_energy.sum)", "sum(calculated_runhour.sum)", "avg(load_percentage.avg)", "max(load_percentage.max)", "sum(calculated_energy_p_fuel_consumption.sum)", "sum(fuel_consumption_p_calculated_runhour.sum)", "sum(fuel_theft.sum)", "sum(fuel_filled.sum)", "sum(fuel_consumption.sum)", "sum(calculated_idle_hour.sum)"], "group_by": "thing", "group_summary": ["sum(calculated_energy.sum)", "sum(calculated_runhour.sum)", "avg(load_percentage.avg)", "max(load_percentage.max)", "sum(calculated_energy_p_fuel_consumption.sum)", "sum(fuel_consumption_p_calculated_runhour.sum)", "sum(fuel_theft.sum)", "sum(fuel_filled.sum)", "sum(fuel_consumption.sum)", "sum(calculated_idle_hour.sum)"], "exclude_inactive": true}, {"query": ["sum(calculated_energy.sum)", "sum(calculated_runhour.sum)", "avg(load_percentage.avg)", "max(load_percentage.max)", "sum(calculated_energy_p_fuel_consumption.sum)", "sum(fuel_consumption_p_calculated_runhour.sum)", "sum(fuel_theft.sum)", "sum(fuel_filled.sum)", "sum(fuel_consumption.sum)", "sum(calculated_idle_hour.sum)"], "group_by": "time", "exclude_inactive": true}], "categories": [18], "parameters": ["calculated_energy", "calculated_runhour", "load_percentage", "calculated_energy_p_fuel_consumption", "fuel_consumption_p_calculated_runhour", "fuel_theft", "fuel_filled", "fuel_consumption", "calculated_idle_hour"], "parameter_attributes": ["sum", "avg", "max", "value"]}, "resizable": false, "tableProps": {"size": "middle", "columns": [{"align": "left", "fixed": "left", "title": "<PERSON><PERSON>", "width": 200, "sorter": "function(a, b) { return a.asset.localeCompare(b.asset); }", "apiType": "asset-list", "ellipsis": true, "dataIndex": "asset", "group_key": "asset_details", "pdf_title": "<PERSON><PERSON>", "valueIndex": 0, "not_customizable": true}, {"key": "eng_sl_no", "align": "left", "title": "Engine Serial No", "apiType": "asset-list", "ellipsis": false, "dataIndex": "eng_sl_no", "group_key": "asset_details", "pdf_title": "Engine Serial No", "colPathExp": "thing_details.engine_sl_no", "valueIndex": 0}, {"key": "gen_sl_no", "align": "left", "title": "Genset Serial No", "apiType": "asset-list", "ellipsis": false, "dataIndex": "gen_sl_no", "group_key": "asset_details", "pdf_title": "Genset Serial No", "colPathExp": "thing_details.genset_sl_no", "valueIndex": 0}, {"key": "make", "align": "left", "title": "Make", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "make", "group_key": "asset_details", "pdf_title": "Make", "colPathExp": "thing_details.make", "valueIndex": 0}, {"key": "model", "align": "left", "title": "Model", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "model", "group_key": "asset_details", "pdf_title": "Model", "colPathExp": "thing_details.model", "valueIndex": 0}, {"key": "kva", "align": "left", "title": "KVA", "sorter": "function(a, b) { return (a.kva === '-' ? 0 : a.kva) - (b.kva === '-' ? 0 : b.kva); }", "apiType": "asset-list", "ellipsis": false, "dataIndex": "kva", "group_key": "asset_details", "pdf_title": "KVA", "colPathExp": "thing_details.kva", "valueIndex": 0}, {"key": "tank_capacity", "align": "left", "title": "Fuel Tank Capacity (L)", "sorter": "function(a, b) { return (a.tank_capacity === '-' ? 0 : a.tank_capacity) - (b.tank_capacity === '-' ? 0 : b.tank_capacity); }", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "tank_capacity", "group_key": "asset_details", "pdf_title": "Fuel Tank Capacity (L)", "colPathExp": "thing_details.capacity", "valueIndex": 0}, {"key": "commissioning_date", "align": "left", "title": "Commission Date", "format": "DD MMM YYYY", "sorter": "function(a, b) { return a.sorter_key_commissioning_date - b.sorter_key_commissioning_date; }", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "commissioning_date", "group_key": "asset_details", "pdf_title": "Commission Date", "colPathExp": "commissioning_date", "sorter_key": true, "valueIndex": 0}, {"key": "lifetime_rnhr", "align": "left", "title": "Lifetime Run Hour (HH:MM)", "format": "HH:mm", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "lifetime_rnhr", "group_key": "asset_details", "pdf_title": "Lifetime Run Hour (HH:MM)", "colPathExp": "thing_details.lifetime_runhour", "multiplier": 3600, "valueIndex": 0}, {"key": "dg_phase", "align": "left", "title": "DG Phase", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "dg_phase", "group_key": "asset_details", "pdf_title": "DG Phase", "colPathExp": "thing_details.dg_type", "valueIndex": 0}, {"key": "device_qr", "align": "left", "title": "<PERSON><PERSON>", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "device_qr", "group_key": "asset_details", "pdf_title": "<PERSON><PERSON>", "colPathExp": "devices[0].qr_code", "valueIndex": 0}, {"key": "vendor_name", "align": "left", "title": "Vendor Name", "apiType": "asset-list", "checked": false, "ellipsis": false, "dataIndex": "vendor_name", "group_key": "asset_details", "pdf_title": "Vendor Name", "colPathExp": "vendor_name", "valueIndex": 0, "applicable_when": "partner_count > 1"}, {"children": [{"title": "Run Hour (HH:MM)", "width": 180, "format": "HH:mm", "sorter": "function(a, b) { return a.total_calculated_runhour.localeCompare(b.total_calculated_runhour); }", "dataIndex": "total_calculated_runhour", "hoverText": "Represents genset running hour", "pdf_title": "Run Hour (HH:MM)", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.calculated_runhour.sum", "valueIndex": 0, "commonColKey": "calculated_runhour"}, {"title": "Idle Hours (HH:MM)", "width": 180, "format": "HH:mm", "sorter": "function(a, b) { return a.total_calculated_idle_hour.localeCompare(b.total_calculated_idle_hour); }", "dataIndex": "total_calculated_idle_hour", "hoverText": "Running Hours where the load % is less than 5%", "pdf_title": "Idle Hours (HH:MM)", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.calculated_idle_hour.sum", "valueIndex": 0, "commonColKey": "calculated_idle_hour"}, {"title": "Energy Generated (kWh)", "width": 200, "sorter": "function(a, b) { return (a.total_calculated_energy === '-' ? 0 : a.total_calculated_energy) - (b.total_calculated_energy === '-' ? 0 : b.total_calculated_energy); }", "dataIndex": "total_calculated_energy", "pdf_title": "Energy Generated (kWh)", "precision": 2, "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.calculated_energy.sum", "valueIndex": 0, "commonColKey": "calculated_energy"}, {"title": "Fuel Consumed (L)", "width": 180, "sorter": "function(a, b) { return (a.total_fuel_consumption === '-' ? 0 : a.total_fuel_consumption) - (b.total_fuel_consumption === '-' ? 0 : b.total_fuel_consumption); }", "dataIndex": "total_fuel_consumption", "pdf_title": "Fuel Consumed (L)", "precision": 2, "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.fuel_consumption.sum", "valueIndex": 0, "commonColKey": "fuel_consumption"}, {"title": "Fuel Filled (L)", "width": 180, "sorter": "function(a, b) { return (a.total_fuel_filled === '-' ? 0 : a.total_fuel_filled) - (b.total_fuel_filled === '-' ? 0 : b.total_fuel_filled); }", "dataIndex": "total_fuel_filled", "pdf_title": "Fuel Filled (L)", "precision": 2, "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.fuel_filled.sum", "valueIndex": 0, "commonColKey": "fuel_filled"}, {"title": "Fuel Drained (L)", "width": 180, "sorter": "function(a, b) { return (a.total_fuel_theft === '-' ? 0 : a.total_fuel_theft) - (b.total_fuel_theft === '-' ? 0 : b.total_fuel_theft); }", "dataIndex": "total_fuel_theft", "hoverText": "Represents the fuel loss amount due to drainage or pilferage during the time period ", "pdf_title": "Fuel Drained (L)", "precision": 2, "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.fuel_theft.sum", "valueIndex": 0, "commonColKey": "fuel_theft"}, {"title": "Fuel Usage Rate (L/Hr)", "width": 180, "sorter": "function(a, b) { return (a.total_fuel_consumption_p_calculated_runhour === '-' ? 0 : a.total_fuel_consumption_p_calculated_runhour) - (b.total_fuel_consumption_p_calculated_runhour === '-' ? 0 : b.total_fuel_consumption_p_calculated_runhour); }", "checked": false, "dataIndex": "total_fuel_consumption_p_calculated_runhour", "pdf_title": "Fuel Usage Rate (L/Hr)", "precision": 2, "colPathExp": "summary[2][1][?asset_id==`{thing_id}`].fuel_consumption_p_calculated_runhour_overall", "valueIndex": 0, "commonColKey": "fuel_consumption_p_calculated_runhour"}, {"title": "Fuel Efficiency (kWh/L)", "width": 180, "sorter": "function(a, b) { return (a.total_calculated_energy_p_fuel_consumption === '-' ? 0 : a.total_calculated_energy_p_fuel_consumption) - (b.total_calculated_energy_p_fuel_consumption === '-' ? 0 : b.total_calculated_energy_p_fuel_consumption); }", "checked": false, "dataIndex": "total_calculated_energy_p_fuel_consumption", "pdf_title": "Fuel Efficiency (kWh/L)", "precision": 2, "colPathExp": "summary[2][1][?asset_id==`{thing_id}`].calculated_energy_p_fuel_consumption_overall", "valueIndex": 0, "commonColKey": "calculated_energy_p_fuel_consumption"}, {"title": "Average Load %", "width": 180, "sorter": "function(a, b) { return (a.total_load_percentage === '-' ? 0 : a.total_load_percentage) - (b.total_load_percentage === '-' ? 0 : b.total_load_percentage); }", "checked": false, "dataIndex": "total_load_percentage", "pdf_title": "Average Load %", "precision": 2, "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.avg.load_percentage.avg", "valueIndex": 0, "commonColKey": "load_percentage"}, {"title": "Peak Load %", "width": 180, "sorter": "function(a, b) { return (a.total_peak_load_percentage === '-' ? 0 : a.total_peak_load_percentage) - (b.total_peak_load_percentage === '-' ? 0 : b.total_peak_load_percentage); }", "checked": false, "dataIndex": "total_peak_load_percentage", "hoverText": "Represents peak load % during the entire duration", "pdf_title": "Peak Load %", "precision": 2, "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.max.load_percentage.max", "valueIndex": 0, "commonColKey": "peak_load_percentage"}], "group_key": "telemetry_data", "column_type": "total", "group_repeated": true}, {"children": [{"title": "Run hour (HH:MM)", "width": 180, "format": "HH:mm", "sorter": "function(a, b) { return a.calculated_runhour_{unixTime}.localeCompare(b.calculated_runhour_{unixTime}); }", "hoverText": "Represents genset running hour", "pdf_title": "Run hour (HH:MM)", "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.calculated_runhour.sum", "valueIndex": 0, "commonColKey": "calculated_runhour", "paramKeyTemplate": "calculated_runhour_{unixTime}"}, {"title": "Idle Hours (HH:MM)", "width": 180, "format": "HH:mm", "sorter": "function(a, b) { return a.calculated_idle_hour_{unixTime}.localeCompare(b.calculated_idle_hour_{unixTime}); }", "hoverText": "Running Hours where the load % is less than 5%", "pdf_title": "Idle Hours (HH:MM)", "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.calculated_idle_hour.sum", "valueIndex": 0, "commonColKey": "calculated_idle_hour", "paramKeyTemplate": "calculated_idle_hour_{unixTime}"}, {"title": "Energy Generated (kWh)", "width": 200, "sorter": "function(a, b) { return (a.calculated_energy_{unixTime} === '-' ? 0 : a.calculated_energy_{unixTime}) - (b.calculated_energy_{unixTime} === '-' ? 0 : b.calculated_energy_{unixTime}); }", "pdf_title": "Energy Generated (kWh)", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.calculated_energy.sum", "valueIndex": 0, "commonColKey": "calculated_energy", "paramKeyTemplate": "calculated_energy_{unixTime}"}, {"title": "Fuel Consumed (L)", "width": 180, "sorter": "function(a, b) { return (a.fuel_consumption_{unixTime} === '-' ? 0 : a.fuel_consumption_{unixTime}) - (b.fuel_consumption_{unixTime} === '-' ? 0 : b.fuel_consumption_{unixTime}); }", "pdf_title": "Fuel Consumed (L)", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.fuel_consumption.sum", "valueIndex": 0, "commonColKey": "fuel_consumption", "paramKeyTemplate": "fuel_consumption_{unixTime}"}, {"title": "Fuel Filled (L)", "width": 180, "sorter": "function(a, b) { return (a.fuel_filled_{unixTime} === '-' ? 0 : a.fuel_filled_{unixTime}) - (b.fuel_filled_{unixTime} === '-' ? 0 : b.fuel_filled_{unixTime}); }", "pdf_title": "Fuel Filled (L)", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.fuel_filled.sum", "valueIndex": 0, "commonColKey": "fuel_filled", "paramKeyTemplate": "fuel_filled_{unixTime}"}, {"title": "Fuel Drained (L)", "width": 180, "sorter": "function(a, b) { return (a.fuel_theft_{unixTime} === '-' ? 0 : a.fuel_theft_{unixTime}) - (b.fuel_theft_{unixTime} === '-' ? 0 : b.fuel_theft_{unixTime}); }", "hoverText": "Represents the fuel loss amount due to drainage or pilferage during the time period ", "pdf_title": "Fuel Drained (L)", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.fuel_theft.sum", "valueIndex": 0, "commonColKey": "fuel_theft", "paramKeyTemplate": "fuel_theft_{unixTime}"}, {"title": "Fuel Usage Rate (L/Hr)", "width": 180, "sorter": "function(a, b) { return (a.fuel_consumption_p_calculated_runhour_{unixTime} === '-' ? 0 : a.fuel_consumption_p_calculated_runhour_{unixTime}) - (b.fuel_consumption_p_calculated_runhour_{unixTime} === '-' ? 0 : b.fuel_consumption_p_calculated_runhour_{unixTime}); }", "checked": false, "pdf_title": "Fuel Usage Rate (L/Hr)", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.fuel_consumption_p_calculated_runhour.value", "valueIndex": 0, "commonColKey": "fuel_consumption_p_calculated_runhour", "paramKeyTemplate": "fuel_consumption_p_calculated_runhour_{unixTime}"}, {"title": "Fuel Efficiency (kWh/L)", "width": 180, "sorter": "function(a, b) { return (a.calculated_energy_p_fuel_consumption_{unixTime} === '-' ? 0 : a.calculated_energy_p_fuel_consumption_{unixTime}) - (b.calculated_energy_p_fuel_consumption_{unixTime} === '-' ? 0 : b.calculated_energy_p_fuel_consumption_{unixTime}); }", "checked": false, "pdf_title": "Fuel Efficiency (kWh/L)", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.calculated_energy_p_fuel_consumption.value", "valueIndex": 0, "commonColKey": "calculated_energy_p_fuel_consumption", "paramKeyTemplate": "calculated_energy_p_fuel_consumption_{unixTime}"}, {"title": "Average Load %", "width": 180, "sorter": "function(a, b) { return (a.load_percentage_{unixTime} === '-' ? 0 : a.load_percentage_{unixTime}) - (b.load_percentage_{unixTime} === '-' ? 0 : b.load_percentage_{unixTime}); }", "checked": false, "pdf_title": "Average Load %", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.load_percentage.avg", "valueIndex": 0, "commonColKey": "load_percentage", "paramKeyTemplate": "load_percentage_{unixTime}"}, {"title": "Peak Load %", "width": 180, "sorter": "function(a, b) { return (a.peak_load_percentage_{unixTime} === '-' ? 0 : a.peak_load_percentage_{unixTime}) - (b.peak_load_percentage_{unixTime} === '-' ? 0 : b.peak_load_percentage_{unixTime}); }", "checked": false, "hoverText": "Represents peak load % during the entire duration", "pdf_title": "Peak Load %", "precision": 2, "colPathExp": "data[?thing_id==`{thing_id}` && time==`{unixTime}`].parameter_values.load_percentage.max", "valueIndex": 0, "commonColKey": "peak_load_percentage", "paramKeyTemplate": "peak_load_percentage_{unixTime}"}], "group_key": "telemetry_data", "column_type": "intervals", "group_repeated": true}], "expandable": false, "pagination": false, "rowSelection": false, "rowTotalConfig": {"totals": [{"format": "HH:mm", "dataIndex": "total_calculated_runhour", "rowPathExp": "summary[0].summary.sum.sum.calculated_runhour.sum"}, {"format": "HH:mm", "dataIndex": "total_calculated_idle_hour", "rowPathExp": "summary[0].summary.sum.sum.calculated_idle_hour.sum"}, {"dataIndex": "total_calculated_energy", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.calculated_energy.sum"}, {"dataIndex": "total_fuel_consumption", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.fuel_consumption.sum"}, {"dataIndex": "total_fuel_filled", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.fuel_filled.sum"}, {"dataIndex": "total_fuel_theft", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.fuel_theft.sum"}, {"dataIndex": "total_fuel_consumption_p_calculated_runhour", "precision": 2, "rowPathExp": "summary[2][2][0].fuel_consumption_p_calculated_runhour_overall"}, {"dataIndex": "total_calculated_energy_p_fuel_consumption", "precision": 2, "rowPathExp": "summary[2][2][0].calculated_energy_p_fuel_consumption_overall"}, {"dataIndex": "total_load_percentage", "precision": 2, "rowPathExp": "summary[0].summary.avg.avg.load_percentage.avg"}, {"dataIndex": "total_peak_load_percentage", "precision": 2, "rowPathExp": "summary[0].summary.max.max.load_percentage.max"}], "showTotalRow": true, "intervalTotals": [{"title": "Total Run Hour (HH:MM)", "format": "HH:mm", "valueIndex": 0, "paramKeyTemplate": "calculated_runhour_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.sum.calculated_runhour.sum"}, {"title": "Idle Run Hour (HH:MM)", "format": "HH:mm", "valueIndex": 0, "paramKeyTemplate": "calculated_idle_hour_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.sum.calculated_idle_hour.sum"}, {"title": "Total Energy (kWh)", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "calculated_energy_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.sum.calculated_energy.sum"}, {"title": "Fuel Consumed (L)", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "fuel_consumption_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.sum.fuel_consumption.sum"}, {"title": "Fuel Filled (L)", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "fuel_filled_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.sum.fuel_filled.sum"}, {"title": "Fuel Drained (L)", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "fuel_theft_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.sum.fuel_theft.sum"}, {"title": "Fuel Usage Rate (L/Hr)", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "fuel_consumption_p_calculated_runhour_{unixTime}", "rowPathExpTemplate": "summary[2][0][?aad_time==`{unixTime}`].fuel_consumption_p_calculated_runhour_overall"}, {"title": "Fuel Efficiency (kWh/L)", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "calculated_energy_p_fuel_consumption_{unixTime}", "rowPathExpTemplate": "summary[2][0][?aad_time==`{unixTime}`].calculated_energy_p_fuel_consumption_overall"}, {"title": "Average Load %", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "load_percentage_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.avg.load_percentage.avg"}, {"title": "Peak Load %", "precision": 2, "valueIndex": 0, "paramKeyTemplate": "peak_load_percentage_{unixTime}", "rowPathExpTemplate": "summary[1].data[?time==`{unixTime}`].parameter_values.max.load_percentage.max"}]}, "columnIntervalConfig": {"dateFormat": "DD MMM YYYY", "showTotalForIntervals": true}}, "dataPathExp": "", "downloadable": false, "preferenceKeys": ["reports", "dg_multi_asset_report"], "defaultFixedCount": 1, "disableCustomization": false, "showTotalRowHighlight": true}], "title": "Multi Asset Report", "graphs": [{"colSpan": 24, "chartType": "line", "highChartOptions": {"chart": {"height": 250, "zoomType": false}, "title": {"text": "Usage Trend"}, "xAxis": {"type": "datetime", "title": {"text": ""}}, "yAxis": [{"title": {"text": "Run Hour (Hrs)"}, "opposite": false}, {"title": {"text": "Fuel Consumption (L)"}, "opposite": true}, {"title": {"text": "Energy Generated (kWh)"}, "opposite": true}], "legend": {"enabled": true}, "series": [{"data": [], "name": "<PERSON><PERSON><PERSON>", "type": "column", "color": "rgba(79, 155, 255, 1)", "yAxis": 0, "marker": {"enabled": false}, "tooltip": {"valueSuffix": " Hrs"}, "api_type": "data", "data_key": "columnChart", "apiConfig": {"data_type": "aggregate", "paramType": "duration", "summarize": [{"query": ["sum(calculated_runhour.sum)"], "group_by": "time"}], "categories": [18]}, "lineWidth": 1, "dataSource": "old", "keyPathExp": "time", "valPathExp": "parameter_values.sum.calculated_runhour.sum", "dataPathExp": "result.summary[0].data", "applicable_when": "{{parameters::calculated_runhour}}"}, {"data": [], "name": "Fuel Consumption", "type": "column", "color": "rgba(51, 115, 255, 1)", "yAxis": 1, "marker": {"enabled": false}, "tooltip": {"valueSuffix": " L"}, "api_type": "data", "data_key": "columnChart", "apiConfig": {"data_type": "aggregate", "summarize": [{"query": ["sum(fuel_consumption.sum)"], "group_by": "time"}], "categories": [18]}, "lineWidth": 1, "dataSource": "old", "keyPathExp": "time", "valPathExp": "parameter_values.sum.fuel_consumption.sum", "dataPathExp": "result.summary[0].data", "applicable_when": "{{parameters::fuel}} || {{parameters::fuel_lt}} || {{parameters::fuel_litre}}"}, {"data": [], "name": "Energy Generated", "type": "column", "color": "rgba(164, 119, 255, 1)", "yAxis": 1, "marker": {"enabled": false}, "tooltip": {"valueSuffix": " kWh"}, "api_type": "data", "data_key": "columnChart", "apiConfig": {"data_type": "aggregate", "summarize": [{"query": ["sum(calculated_energy.sum)"], "group_by": "time"}], "categories": [18]}, "lineWidth": 1, "dataSource": "old", "keyPathExp": "time", "valPathExp": "parameter_values.sum.calculated_energy.sum", "dataPathExp": "result.summary[0].data", "applicable_when": "{{parameters::calculated_energy}}"}], "tooltip": {"formatter": "function() { let date = new Date(this.x); let dateLine = `<span style='font-size: 10px'>${date.getDate()} ${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}</span><br/>`; let seriesLines = this.points.map(point => { if (point.series.name === 'Runhour') { if (point.y !== null) { let totalMinutes = Math.floor(point.y * 60); let hours = Math.floor(totalMinutes / 60); let minutes = totalMinutes % 60; return `<span style='color:${point.series.color}; font-weight:bold;'>${point.series.name}</span>: <b>${hours}h ${minutes}m</b>`; } return `<span style='color:${point.series.color}; font-weight:bold;'>${point.series.name}</span>: <b>No Data</b>`; } else { const suffix = point.series.options.tooltip?.valueSuffix || ''; return `<span style='color:${point.series.color}; font-weight:bold;'>${point.series.name}</span>: <b>${point.y}${suffix}</b>`; } }).join('<br/>'); return `${dateLine}${seriesLines}`; }"}, "animation": true, "exporting": {"buttons": {"contextButton": {"menuItems": ["viewFullscreen"]}}}, "plotOptions": {"line": {"marker": {"enabled": false}, "dataLabels": {"enabled": false}}}}, "min_time_range_days": 7}, {"colSpan": 24, "chartType": "line", "highChartOptions": {"chart": {"height": 250, "zoomType": false}, "title": {"text": "Fuel Events"}, "xAxis": {"type": "datetime", "title": {"text": ""}}, "yAxis": [{"title": {"text": "Fuel Filled (L)"}, "opposite": false}, {"title": {"text": "Fuel Drained (L)"}, "opposite": true}], "legend": {"enabled": true}, "series": [{"data": [], "name": "Fuel Filled", "type": "column", "color": "rgba(245, 149, 89, 1)", "yAxis": 0, "marker": {"enabled": false}, "tooltip": {"valueSuffix": " L"}, "api_type": "data", "data_key": "columnChart", "apiConfig": {"data_type": "aggregate", "summarize": [{"query": ["sum(fuel_filled.sum)"], "group_by": "time"}], "categories": [18]}, "lineWidth": 1, "dataSource": "old", "keyPathExp": "time", "valPathExp": "parameter_values.sum.fuel_filled.sum", "dataPathExp": "result.summary[0].data", "applicable_when": "{{parameters::fuel}} || {{parameters::fuel_lt}} || {{parameters::fuel_litre}}"}, {"data": [], "name": "Fuel Drained", "type": "column", "color": "rgba(255, 136, 91, 1)", "yAxis": 1, "marker": {"enabled": false}, "tooltip": {"valueSuffix": " L"}, "api_type": "data", "data_key": "columnChart", "apiConfig": {"data_type": "aggregate", "summarize": [{"query": ["sum(fuel_theft.sum)"], "group_by": "time"}], "categories": [18]}, "lineWidth": 1, "dataSource": "old", "keyPathExp": "time", "valPathExp": "parameter_values.sum.fuel_theft.sum", "dataPathExp": "result.summary[0].data", "applicable_when": "{{parameters::fuel}} || {{parameters::fuel_lt}} || {{parameters::fuel_litre}}"}], "tooltip": {"formatter": "function() { let date = new Date(this.x); let dateLine = `<span style='font-size: 10px'>${date.getDate()} ${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}</span><br/>`; let seriesLines = this.points.map(point => { if (point.series.name === 'Runhour') { if (point.y !== null) { let totalMinutes = Math.floor(point.y * 60); let hours = Math.floor(totalMinutes / 60); let minutes = totalMinutes % 60; return `<span style='color:${point.series.color}; font-weight:bold;'>${point.series.name}</span>: <b>${hours}h ${minutes}m</b>`; } return `<span style='color:${point.series.color}; font-weight:bold;'>${point.series.name}</span>: <b>No Data</b>`; } else { const suffix = point.series.options.tooltip?.valueSuffix || ''; return `<span style='color:${point.series.color}; font-weight:bold;'>${point.series.name}</span>: <b>${point.y}${suffix}</b>`; } }).join('<br/>'); return `${dateLine}${seriesLines}`; }"}, "animation": true, "exporting": {"buttons": {"contextButton": {"menuItems": ["viewFullscreen"]}}}, "plotOptions": {"line": {"marker": {"enabled": false}, "dataLabels": {"enabled": false}}}}, "min_time_range_days": 7}], "filters": ["aggregation_period", "things"], "summary": [{"data": [{"key": "total_assets", "text": "Total DG Sets", "unit": "", "valPathExp": "total_assets"}, {"key": "running_asset_count", "text": "Running Asset Count", "unit": "", "valPathExp": "summary[0].data[0].parameter_values.count.calculated_runhour.sum"}, {"key": "calculated_runhour", "text": "Total Run Hour", "unit": "Hrs", "format": "HH:mm", "valPathExp": "summary[0].data[0].parameter_values.sum.calculated_runhour.sum", "description": "Total Run Hour"}, {"key": "fuel_consumption", "text": "Fuel Consumed", "unit": "L", "precision": 2, "valPathExp": "summary[0].data[0].parameter_values.sum.fuel_consumption.sum", "description": "Total Fuel Consumption"}, {"key": "fuel_filled", "text": "Fuel Filled", "unit": "L", "precision": 2, "valPathExp": "summary[0].data[0].parameter_values.sum.fuel_filled.sum", "description": "Total Fuel Filled shown in Liters"}, {"key": "fuel_theft", "text": "Fuel Drained", "unit": "L", "precision": 2, "valPathExp": "summary[0].data[0].parameter_values.sum.fuel_theft.sum", "description": "Total Fuel Drained shown in Liters"}, {"key": "calculated_energy", "text": "Energy Generated", "unit": "kWh", "precision": 2, "valPathExp": "summary[0].data[0].parameter_values.sum.calculated_energy.sum", "description": "Total Energy Generated"}], "config": {"title": "Summary", "variant": "horizontal", "bordered": false, "pipeline": true, "noDataText": "No summary data found for the selected period", "maxItemInRow": 4}, "apiType": "data", "apiConfig": {"data_type": "aggregate", "summarize": [{"query": ["sum(calculated_energy.sum)", "sum(calculated_runhour.sum)", "sum(def_consumption.sum)", "sum(fuel_consumption.sum)", "sum(fuel_filled.sum)", "sum(fuel_theft.sum)", "count(calculated_runhour.sum > 0)"]}], "categories": [18], "get_total_assets": true}, "dataPathExp": "", "preferenceKeys": ["reports", "dg_multi_asset_report"]}], "no_aggregation": {"table": [{"preferenceKeys": ["reports", "dg_multi_asset_report"], "disableCustomization": false, "showTotalRowHighlight": true, "defaultFixedCount": 1, "colSpan": 24, "title": "Detailed Data", "downloadable": false, "resizable": false, "tableProps": {"rowSelection": false, "pagination": false, "expandable": false, "columns": [{"title": "<PERSON><PERSON>", "dataIndex": "asset", "apiType": "asset-list", "valueIndex": 0, "pdf_title": "<PERSON><PERSON>", "align": "left", "fixed": "left", "ellipsis": true, "not_customizable": true, "width": 200, "sorter": "function(a, b) { return a.asset.localeCompare(b.asset); }"}, {"title": "Engine Serial No", "dataIndex": "eng_sl_no", "colPathExp": "thing_details.engine_sl_no", "apiType": "asset-list", "valueIndex": 0, "key": "eng_sl_no", "pdf_title": "Engine Serial No", "align": "left", "ellipsis": false}, {"title": "Genset Serial No", "dataIndex": "gen_sl_no", "colPathExp": "thing_details.genset_sl_no", "apiType": "asset-list", "valueIndex": 0, "key": "gen_sl_no", "pdf_title": "Genset Serial No", "align": "left", "ellipsis": false}, {"title": "Make", "dataIndex": "make", "colPathExp": "thing_details.make", "apiType": "asset-list", "valueIndex": 0, "key": "make", "pdf_title": "Make", "align": "left", "ellipsis": false, "checked": false}, {"title": "Model", "dataIndex": "model", "colPathExp": "thing_details.model", "apiType": "asset-list", "valueIndex": 0, "key": "model", "pdf_title": "Model", "align": "left", "ellipsis": false, "checked": false}, {"title": "KVA", "dataIndex": "kva", "colPathExp": "thing_details.kva", "apiType": "asset-list", "valueIndex": 0, "key": "kva", "pdf_title": "KVA", "align": "left", "ellipsis": false, "sorter": "function(a, b) { return (a.kva === '-' ? 0 : a.kva) - (b.kva === '-' ? 0 : b.kva); }"}, {"title": "Fuel Tank Capacity (L)", "dataIndex": "tank_capacity", "colPathExp": "thing_details.capacity", "apiType": "asset-list", "valueIndex": 0, "key": "tank_capacity", "pdf_title": "Fuel Tank Capacity (L)", "align": "left", "ellipsis": false, "checked": false, "sorter": "function(a, b) { return (a.tank_capacity === '-' ? 0 : a.tank_capacity) - (b.tank_capacity === '-' ? 0 : b.tank_capacity); }"}, {"title": "Commission Date", "dataIndex": "commissioning_date", "colPathExp": "commissioning_date", "apiType": "asset-list", "valueIndex": 0, "format": "DD MMM YYYY", "key": "commissioning_date", "pdf_title": "Commission Date", "align": "left", "ellipsis": false, "checked": false, "sorter_key": true, "sorter": "function(a, b) { return a.sorter_key_commissioning_date - b.sorter_key_commissioning_date; }"}, {"title": "Lifetime Run Hour (HH:MM)", "dataIndex": "lifetime_rnhr", "colPathExp": "thing_details.lifetime_runhour", "apiType": "asset-list", "valueIndex": 0, "format": "HH:mm", "multiplier": 3600, "key": "lifetime_rnhr", "pdf_title": "Lifetime Run Hour (HH:MM)", "align": "left", "ellipsis": false, "checked": false}, {"title": "DG Phase", "dataIndex": "dg_phase", "colPathExp": "thing_details.dg_type", "apiType": "asset-list", "valueIndex": 0, "key": "dg_phase", "pdf_title": "DG Phase", "align": "left", "ellipsis": false, "checked": false}, {"title": "<PERSON><PERSON>", "dataIndex": "device_qr", "colPathExp": "devices[0].qr_code", "apiType": "asset-list", "valueIndex": 0, "key": "device_qr", "pdf_title": "<PERSON><PERSON>", "align": "left", "ellipsis": false, "checked": false}, {"title": "Vendor Name", "dataIndex": "vendor_name", "colPathExp": "vendor_name", "apiType": "asset-list", "valueIndex": 0, "key": "vendor_name", "pdf_title": "Vendor Name", "align": "left", "ellipsis": false, "applicable_when": "partner_count > 1", "checked": false}, {"title": "Run Hour (HH:MM)", "pdf_title": "Run Hour (HH:MM)", "commonColKey": "calculated_runhour", "dataIndex": "total_calculated_runhour", "hoverText": "Represents genset running hour", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.calculated_runhour.sum", "valueIndex": 0, "format": "HH:mm", "width": 180, "sorter": "function(a, b) { return a.total_calculated_runhour.localeCompare(b.total_calculated_runhour); }"}, {"title": "Idle Hours (HH:MM)", "pdf_title": "Idle Hours (HH:MM)", "hoverText": "Running Hours where the load % is less than 5%", "commonColKey": "calculated_idle_hour", "dataIndex": "total_calculated_idle_hour", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.calculated_idle_hour.sum", "valueIndex": 0, "format": "HH:mm", "width": 180, "sorter": "function(a, b) { return a.total_calculated_idle_hour.localeCompare(b.total_calculated_idle_hour); }"}, {"title": "Energy Generated (kWh)", "pdf_title": "Energy Generated (kWh)", "commonColKey": "calculated_energy", "dataIndex": "total_calculated_energy", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.calculated_energy.sum", "valueIndex": 0, "precision": 2, "width": 200, "sorter": "function(a, b) { return (a.total_calculated_energy === '-' ? 0 : a.total_calculated_energy) - (b.total_calculated_energy === '-' ? 0 : b.total_calculated_energy); }"}, {"title": "Fuel Consumed (L)", "width": 180, "pdf_title": "Fuel Consumed (L)", "commonColKey": "fuel_consumption", "dataIndex": "total_fuel_consumption", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.fuel_consumption.sum", "valueIndex": 0, "precision": 2, "sorter": "function(a, b) { return (a.total_fuel_consumption === '-' ? 0 : a.total_fuel_consumption) - (b.total_fuel_consumption === '-' ? 0 : b.total_fuel_consumption); }"}, {"title": "Fuel Filled (L)", "width": 180, "pdf_title": "Fuel Filled (L)", "commonColKey": "fuel_filled", "dataIndex": "total_fuel_filled", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.fuel_filled.sum", "valueIndex": 0, "precision": 2, "sorter": "function(a, b) { return (a.total_fuel_filled === '-' ? 0 : a.total_fuel_filled) - (b.total_fuel_filled === '-' ? 0 : b.total_fuel_filled); }"}, {"title": "Fuel Drained (L)", "width": 180, "pdf_title": "Fuel Drained (L)", "commonColKey": "fuel_theft", "hoverText": "Represents the fuel loss amount due to drainage or pilferage during the time period ", "dataIndex": "total_fuel_theft", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.sum.fuel_theft.sum", "valueIndex": 0, "precision": 2, "sorter": "function(a, b) { return (a.total_fuel_theft === '-' ? 0 : a.total_fuel_theft) - (b.total_fuel_theft === '-' ? 0 : b.total_fuel_theft); }"}, {"title": "Fuel Usage Rate (L/Hr)", "width": 180, "pdf_title": "Fuel Usage Rate (L/Hr)", "commonColKey": "fuel_consumption_p_calculated_runhour", "dataIndex": "total_fuel_consumption_p_calculated_runhour", "colPathExp": "summary[1][1][?asset_id==`{thing_id}`].fuel_consumption_p_calculated_runhour_overall", "valueIndex": 0, "precision": 2, "checked": false, "sorter": "function(a, b) { return (a.total_fuel_consumption_p_calculated_runhour === '-' ? 0 : a.total_fuel_consumption_p_calculated_runhour) - (b.total_fuel_consumption_p_calculated_runhour === '-' ? 0 : b.total_fuel_consumption_p_calculated_runhour); }"}, {"title": "Fuel Efficiency (kWh/L)", "width": 180, "pdf_title": "Fuel Efficiency (kWh/L)", "commonColKey": "calculated_energy_p_fuel_consumption", "dataIndex": "total_calculated_energy_p_fuel_consumption", "colPathExp": "summary[1][1][?asset_id==`{thing_id}`].calculated_energy_p_fuel_consumption_overall", "valueIndex": 0, "precision": 2, "checked": false, "sorter": "function(a, b) { return (a.total_calculated_energy_p_fuel_consumption === '-' ? 0 : a.total_calculated_energy_p_fuel_consumption) - (b.total_calculated_energy_p_fuel_consumption === '-' ? 0 : b.total_calculated_energy_p_fuel_consumption); }"}, {"title": "Average Load %", "width": 180, "pdf_title": "Average Load %", "commonColKey": "load_percentage", "dataIndex": "total_load_percentage", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.avg.load_percentage.avg", "valueIndex": 0, "precision": 2, "checked": false, "sorter": "function(a, b) { return (a.total_load_percentage === '-' ? 0 : a.total_load_percentage) - (b.total_load_percentage === '-' ? 0 : b.total_load_percentage); }"}, {"title": "Peak Load %", "width": 180, "pdf_title": "Peak Load %", "commonColKey": "peak_load_percentage", "precision": 2, "hoverText": "Represents peak load % during the entire duration", "dataIndex": "total_peak_load_percentage", "colPathExp": "summary[0].data[?thing_id==`{thing_id}`].parameter_values.max.load_percentage.max", "valueIndex": 0, "checked": false, "sorter": "function(a, b) { return (a.total_peak_load_percentage === '-' ? 0 : a.total_peak_load_percentage) - (b.total_peak_load_percentage === '-' ? 0 : b.total_peak_load_percentage); }"}], "rowTotalConfig": {"showTotalRow": true, "totals": [{"dataIndex": "total_calculated_runhour", "format": "HH:mm", "rowPathExp": "summary[0].summary.sum.sum.calculated_runhour.sum"}, {"dataIndex": "total_calculated_idle_hour", "format": "HH:mm", "rowPathExp": "summary[0].summary.sum.sum.calculated_idle_hour.sum"}, {"dataIndex": "total_calculated_energy", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.calculated_energy.sum"}, {"dataIndex": "total_fuel_consumption", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.fuel_consumption.sum"}, {"dataIndex": "total_fuel_filled", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.fuel_filled.sum"}, {"dataIndex": "total_fuel_theft", "precision": 2, "rowPathExp": "summary[0].summary.sum.sum.fuel_theft.sum"}, {"dataIndex": "total_fuel_consumption_p_calculated_runhour", "precision": 2, "rowPathExp": "summary[1][2][0].fuel_consumption_p_calculated_runhour_overall"}, {"dataIndex": "total_calculated_energy_p_fuel_consumption", "precision": 2, "rowPathExp": "summary[1][2][0].calculated_energy_p_fuel_consumption_overall"}, {"dataIndex": "total_load_percentage", "precision": 2, "rowPathExp": "summary[0].summary.avg.avg.load_percentage.avg"}, {"dataIndex": "total_peak_load_percentage", "precision": 2, "rowPathExp": "summary[0].summary.max.max.load_percentage.max"}]}, "size": "middle"}, "apiConfig": {"data_type": "aggregate", "parameters": [], "things": [], "categories": [18], "api_query": {"thing_category": "18"}, "summarize": [{"query": ["sum(calculated_energy.sum)", "sum(calculated_runhour.sum)", "avg(load_percentage.avg)", "max(load_percentage.max)", "sum(calculated_energy_p_fuel_consumption.sum)", "sum(fuel_consumption_p_calculated_runhour.sum)", "sum(fuel_theft.sum)", "sum(fuel_filled.sum)", "sum(fuel_consumption.sum)", "sum(calculated_idle_hour.sum)"], "group_summary": ["sum(calculated_energy.sum)", "sum(calculated_runhour.sum)", "avg(load_percentage.avg)", "max(load_percentage.max)", "sum(calculated_energy_p_fuel_consumption.sum)", "sum(fuel_consumption_p_calculated_runhour.sum)", "sum(fuel_theft.sum)", "sum(fuel_filled.sum)", "sum(fuel_consumption.sum)", "sum(calculated_idle_hour.sum)"], "group_by": "thing", "exclude_inactive": true}], "summary": [{"parameters": ["fuel_consumption_p_calculated_runhour.overall", "calculated_energy_p_fuel_consumption.overall"], "group_by": "time"}, {"parameters": ["fuel_consumption_p_calculated_runhour.overall", "calculated_energy_p_fuel_consumption.overall"], "group_by": "thing_id"}, {"parameters": ["fuel_consumption_p_calculated_runhour.overall", "calculated_energy_p_fuel_consumption.overall"]}]}, "apiType": "data", "dataPathExp": ""}], "graphs": []}, "data_source": "telemetry", "header_text": "Multi Asset Report", "report_type": "multi_entity", "thingCategory": 18, "datePickerConfig": {"size": "default", "format": "DD-MMM-YYYY", "showTime": false, "separator": " - ", "placeholder": ["From", "To"]}, "datePickerRanges": ["Today", "Yesterday", "Last 7 Days", "This Week", "Last Week", "Last 30 Days", "This Month", "Last Month", "Last 90 Days", "This Quarter", "Last Quarter", "Custom"], "defaultDateRange": "Last 7 Days", "showAssetDetails": false, "date_range_format": "DD MMM YYYY", "date_range_picker": "date", "max_time_range_days": 1095, "default_time_range_days": 30, "customRangePickerEnabled": true, "aggregation_period_options": [{"label": "None", "value": "none"}, {"label": "Daily", "value": 86400}, {"label": "Weekly", "value": 604800}, {"label": "Monthly", "value": 2592000}, {"label": "Quarterly", "value": 7776000}, {"label": "Yearly", "value": 31536000}], "default_aggregation_period": "none"}