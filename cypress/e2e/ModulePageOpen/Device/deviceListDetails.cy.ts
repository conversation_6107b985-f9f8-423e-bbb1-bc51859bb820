/// <reference types="cypress" />

import { startPageLoadTimer, endPageLoadTimer } from '../../../utils/pageLoad';
import { harConfig, saveAndProcessHar } from '../../../utils/harUtils';
import { logPageLoadTime } from '../../../utils/csvLogger';
import { setPageUrl } from '../../../utils/setPageUrl';
import { isFileExecutedDirectly } from '../../../utils/checkSpecRun';
import { pageSelectorId } from '../../../fixtures/pageSelector';

// Import existing device test functions
import { openDeviceConfigurePage } from './deviceConfigurePage.cy';
import { openDeviceCustomCommandPage } from './deviceCustomCommandPage.cy';
import { openDeviceDebugOfflineTrendPage } from './deviceDebugPage.cy';
import { openDeviceUnassignedPage } from './deviceUnassignedPageOpen.cy';

// Default CSV paths from Cypress environment
const { pageLoadCsvPath: defaultPageLoadCsvPath, apiResponseCsvPath: defaultApiResponseCsvPath } = Cypress.env();
const moduleName = 'Device';

/**
 * Test the Device List Details functionality
 * @param pageLoadCsvPath Path to CSV file for page load times
 * @param apiResponseCsvPath Path to CSV file for API response times
 * @param customerType Type of customer (DatomsX, etc.)
 */
export const testDeviceListDetails = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType?: string
) => {
    const customer_type = customerType || 'DatomsX';

    cy.log('Starting Device List Details Tests');

    // Test device table interactions
    testDeviceTableInteractions(pageLoadCsvPath, apiResponseCsvPath, customer_type);

    // // Test device action buttons and functionality
    // testDeviceActionButtons(pageLoadCsvPath, apiResponseCsvPath, customer_type);

    // // Test device status functionality
    // testDeviceStatusFunctionality(pageLoadCsvPath, apiResponseCsvPath, customer_type);

    // // Test device search and filter functionality
    // testDeviceSearchAndFilter(pageLoadCsvPath, apiResponseCsvPath, customer_type);

    // // Test device configuration access (if applicable)
    // if (customer_type === 'DatomsX') {
    //     testDeviceConfiguration(pageLoadCsvPath, apiResponseCsvPath, customer_type);
    // }

    // // Test device custom commands (if applicable)
    // if (customer_type !== 'DatomsX') {
    //     testDeviceCustomCommands(pageLoadCsvPath, apiResponseCsvPath, customer_type);
    // }

    // // Test device debug functionality
    // testDeviceDebugFunctionality(pageLoadCsvPath, apiResponseCsvPath, customer_type);

    // // Test unassigned devices functionality
    // testUnassignedDeviceFunctionality(pageLoadCsvPath, apiResponseCsvPath, customer_type);
};

/**
 * Test device table interactions like sorting, filtering, pagination
 */
export const testDeviceTableInteractions = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    const pageLoadData = { startTime: 0, loadTime: 0 };
    const tag = "Action";
    const pageName = "Device-Table-Interactions";

    cy.then(() => {
        cy.recordHar(harConfig);
        pageLoadData.startTime = startPageLoadTimer();
    });

    // Test table sorting functionality
    cy.get('.ant-table-container').scrollIntoView();
    // cy.get('.ant-table-thead th').first().click(); // Sort by first column
    // cy.wait(1000); // Wait for sorting to complete

    // Test pagination if available
    cy.get('body').then($body => {
        if ($body.find('.ant-pagination').length > 0) {
            cy.get('.ant-pagination').should('be.visible');
            // Test page size change if available
            if ($body.find('.ant-select-selector').length > 0) {
                cy.get('.ant-select-selector').first().click();
                cy.get('.ant-select-item').contains('15').click();
                cy.wait(1000);
            }
        }
    });

    cy.then(() => {
        pageLoadData.loadTime = endPageLoadTimer(pageLoadData.startTime);
        logPageLoadTime(pageLoadCsvPath, pageName, moduleName, pageLoadData.loadTime, tag, customerType);
        saveAndProcessHar(pageName, moduleName, apiResponseCsvPath, tag, customerType);
    });
};

/**
 * Test device action buttons and basic functionality
 */
export const testDeviceActionButtons = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    const pageLoadData = { startTime: 0, loadTime: 0 };
    const tag = "Action";
    const pageName = "Device-Action-Buttons";

    cy.then(() => {
        cy.recordHar(harConfig);
        pageLoadData.startTime = startPageLoadTimer();
    });

    // Test device row selection
    cy.get('.ant-table-row').first().click();
    cy.wait(500);

    // Test action buttons visibility
    cy.get('body').then($body => {
        // Check for common device action buttons
        if ($body.find('[data-testid="device-configure"]').length > 0) {
            cy.get('[data-testid="device-configure"]').should('be.visible');
        }

        if ($body.find('[data-testid="device-debug"]').length > 0) {
            cy.get('[data-testid="device-debug"]').should('be.visible');
        }

        if ($body.find('[data-testid="custom-command"]').length > 0) {
            cy.get('[data-testid="custom-command"]').should('be.visible');
        }
    });

    cy.then(() => {
        pageLoadData.loadTime = endPageLoadTimer(pageLoadData.startTime);
        logPageLoadTime(pageLoadCsvPath, pageName, moduleName, pageLoadData.loadTime, tag, customerType);
        saveAndProcessHar(pageName, moduleName, apiResponseCsvPath, tag, customerType);
    });
};

/**
 * Test device configuration functionality
 */
export const testDeviceConfiguration = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    cy.log('Testing Device Configuration functionality');
    openDeviceConfigurePage(pageLoadCsvPath, apiResponseCsvPath, customerType);
};

/**
 * Test device custom commands functionality
 */
export const testDeviceCustomCommands = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    cy.log('Testing Device Custom Commands functionality');
    openDeviceCustomCommandPage('assigned', pageLoadCsvPath, apiResponseCsvPath, customerType);
};

/**
 * Test device debug functionality
 */
export const testDeviceDebugFunctionality = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    cy.log('Testing Device Debug functionality');
    openDeviceDebugOfflineTrendPage('assigned', pageLoadCsvPath, apiResponseCsvPath, customerType);
};

/**
 * Test unassigned device functionality
 */
export const testUnassignedDeviceFunctionality = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    cy.log('Testing Unassigned Device functionality');
    openDeviceUnassignedPage(pageLoadCsvPath, apiResponseCsvPath, customerType);
};

/**
 * Test device status and online/offline functionality
 */
export const testDeviceStatusFunctionality = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    const pageLoadData = { startTime: 0, loadTime: 0 };
    const tag = "Action";
    const pageName = "Device-Status-Check";

    cy.then(() => {
        cy.recordHar(harConfig);
        pageLoadData.startTime = startPageLoadTimer();
    });

    // Check device status indicators in the table
    cy.get('.ant-table-container').scrollIntoView();
    cy.get('.ant-table-row').first().within(() => {
        // Look for status indicators (online/offline badges, status icons, etc.)
        cy.get('body').then($body => {
            if ($body.find('.device-status').length > 0) {
                cy.get('.device-status').should('be.visible');
            }
            if ($body.find('.status-badge').length > 0) {
                cy.get('.status-badge').should('be.visible');
            }
            if ($body.find('.ant-badge').length > 0) {
                cy.get('.ant-badge').should('be.visible');
            }
        });
    });

    cy.then(() => {
        pageLoadData.loadTime = endPageLoadTimer(pageLoadData.startTime);
        logPageLoadTime(pageLoadCsvPath, pageName, moduleName, pageLoadData.loadTime, tag, customerType);
        saveAndProcessHar(pageName, moduleName, apiResponseCsvPath, tag, customerType);
    });
};

/**
 * Test device search and filter functionality
 */
export const testDeviceSearchAndFilter = (
    pageLoadCsvPath: string = defaultPageLoadCsvPath,
    apiResponseCsvPath: string = defaultApiResponseCsvPath,
    customerType: string = 'DatomsX'
) => {
    const pageLoadData = { startTime: 0, loadTime: 0 };
    const tag = "Action";
    const pageName = "Device-Search-Filter";

    cy.then(() => {
        cy.recordHar(harConfig);
        pageLoadData.startTime = startPageLoadTimer();
    });

    // Test search functionality if available
    cy.get('body').then($body => {
        if ($body.find('input[placeholder*="Search"]').length > 0) {
            cy.get('input[placeholder*="Search"]').first().type('test');
            cy.wait(1000);
            cy.get('input[placeholder*="Search"]').first().clear();
        }

        // Test filter dropdowns if available
        if ($body.find('.ant-select').length > 0) {
            cy.get('.ant-select').first().click();
            cy.get('.ant-select-item').first().click();
            cy.wait(1000);
        }
    });

    cy.then(() => {
        pageLoadData.loadTime = endPageLoadTimer(pageLoadData.startTime);
        logPageLoadTime(pageLoadCsvPath, pageName, moduleName, pageLoadData.loadTime, tag, customerType);
        saveAndProcessHar(pageName, moduleName, apiResponseCsvPath, tag, customerType);
    });
};

if (isFileExecutedDirectly(__filename)) {
    describe('Device List Details Page - Functionality Testing', () => {
        before('Log in to the portal', () => {
            cy.fixture('userCredentials').then((users) => {
                cy.login(users.datomsXAdmin.email, users.datomsXAdmin.password, true);
            });
        });

        it('should test all device details functionality', () => {
            // Navigate to device list page first
            cy.visit(`${setPageUrl('DatomsX')}/devices/list`);
            cy.get('.ant-table-container').should('be.visible');

            // Run device details tests
            testDeviceListDetails();
        });
    });
}
